# Windows任务计划程序配置说明

## 概述
本文档详细说明如何配置Windows任务计划程序，实现微信公众号文章的全自动化爬取。

## 前置准备

### 1. 确认环境配置
- ✅ Python环境已安装并添加到PATH
- ✅ 项目依赖已安装：`pip install -r requirements.txt`
- ✅ 微信PC版已安装并保持登录状态
- ✅ 微信PC版代理已配置为 `127.0.0.1:8080`
- ✅ `target_articles.xlsx` 文件已准备并填入有效数据

### 2. 测试自动化脚本
在配置任务计划程序之前，请先手动测试脚本是否正常工作：

```bash
# 方法1: 直接运行Python脚本
python main_enhanced.py --auto

# 方法2: 运行批处理文件
run_auto_crawler.bat

# 方法3: 运行PowerShell脚本
powershell -ExecutionPolicy Bypass -File run_auto_crawler.ps1
```

## 配置Windows任务计划程序

### 步骤1: 打开任务计划程序
1. 按 `Win + R` 打开运行对话框
2. 输入 `taskschd.msc` 并按回车
3. 或者在开始菜单搜索"任务计划程序"

### 步骤2: 创建基本任务
1. 在右侧操作面板点击"创建基本任务..."
2. 输入任务名称：`微信公众号自动爬取`
3. 输入描述：`每日自动爬取微信公众号文章内容和阅读量数据`
4. 点击"下一步"

### 步骤3: 设置触发器
1. 选择触发频率：`每天`
2. 点击"下一步"
3. 设置开始时间（建议选择凌晨时段，如 `02:00:00`）
4. 设置重复间隔：`每天`
5. 点击"下一步"

### 步骤4: 设置操作
选择以下三种方法之一：

#### 方法1: 使用批处理文件（推荐）
1. 选择"启动程序"
2. 点击"下一步"
3. 程序或脚本：`D:\mynj\wechat_spider\run_auto_crawler.bat`
4. 起始于：`D:\mynj\wechat_spider`
5. 点击"下一步"

#### 方法2: 使用PowerShell脚本
1. 选择"启动程序"
2. 点击"下一步"
3. 程序或脚本：`powershell.exe`
4. 添加参数：`-ExecutionPolicy Bypass -File "D:\mynj\wechat_spider\run_auto_crawler.ps1"`
5. 起始于：`D:\mynj\wechat_spider`
6. 点击"下一步"

#### 方法3: 直接调用Python
1. 选择"启动程序"
2. 点击"下一步"
3. 程序或脚本：`python.exe`
4. 添加参数：`main_enhanced.py --auto`
5. 起始于：`D:\mynj\wechat_spider`
6. 点击"下一步"

### 步骤5: 完成创建
1. 检查设置摘要
2. 勾选"当单击'完成'时，打开此任务属性的对话框"
3. 点击"完成"

### 步骤6: 高级设置（重要）
在打开的属性对话框中进行以下设置：

#### 常规选项卡
- ✅ 勾选"不管用户是否登录都要运行"
- ✅ 勾选"使用最高权限运行"
- 选择"配置为"：`Windows 10`

#### 触发器选项卡
- 点击"编辑"按钮
- ✅ 勾选"已启用"
- 在"高级设置"中：
  - ✅ 勾选"重复任务间隔"：`1小时`，持续时间：`无限期`（可选）
  - ✅ 勾选"如果任务失败，重新启动间隔"：`10分钟`，尝试重新启动：`3次`

#### 操作选项卡
- 确认操作设置正确

#### 条件选项卡
- ❌ 取消勾选"只有在计算机使用交流电源时才启动此任务"
- ❌ 取消勾选"只有在以下网络连接可用时才启动此任务"

#### 设置选项卡
- ✅ 勾选"允许按需运行任务"
- ✅ 勾选"如果请求后任务不结束，强行将其停止"，时间限制：`2小时`
- ✅ 勾选"如果任务已经运行，以下规则适用"：`不要启动新实例`

### 步骤7: 保存并测试
1. 点击"确定"保存设置
2. 在任务计划程序库中找到刚创建的任务
3. 右键点击任务，选择"运行"进行测试
4. 查看"历史记录"选项卡确认任务执行状态

## 监控和维护

### 日志文件位置
- 程序日志：`D:\mynj\wechat_spider\logs\`
- 任务计划程序日志：Windows事件查看器 → Windows日志 → 系统

### 结果文件位置
- Excel文件：`D:\mynj\wechat_spider\data\readnum_batch\`
- JSON文件：`D:\mynj\wechat_spider\data\readnum_batch\`

### 常见问题排查
1. **任务未执行**：检查触发器设置和用户权限
2. **程序执行失败**：查看logs目录下的日志文件
3. **Cookie获取失败**：确认微信代理设置和mitmproxy状态
4. **UI自动化失败**：确认微信PC版处于登录状态

### 维护建议
- 定期检查任务执行历史记录
- 定期更新`target_articles.xlsx`中的文章链接
- 定期清理logs目录中的旧日志文件
- 监控data目录的磁盘空间使用情况

## 安全注意事项
- 确保微信账号安全，避免频繁操作触发风控
- 定期备份重要数据和配置文件
- 监控程序运行状态，及时处理异常情况

## 故障恢复
如果任务执行失败，可以：
1. 手动运行批处理文件进行测试
2. 检查并更新微信Cookie
3. 重新配置微信代理设置
4. 联系技术支持获取帮助
