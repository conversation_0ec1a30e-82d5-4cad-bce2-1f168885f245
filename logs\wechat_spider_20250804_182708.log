2025-08-04 18:27:08,298 - INFO - ================================================================================
2025-08-04 18:27:08,298 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-04 18:27:08,298 - INFO - ================================================================================
2025-08-04 18:27:08,298 - INFO - 版本: v3.0 - 全自动化版本
2025-08-04 18:27:08,298 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-04 18:27:08,298 - INFO - 执行时间: 2025-08-04 18:27:08
2025-08-04 18:27:08,299 - INFO - ================================================================================
2025-08-04 18:27:08,300 - INFO - 启动全新自动化爬取流程...
2025-08-04 18:27:08,300 - INFO - ================================================================================
2025-08-04 18:27:08,300 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 18:27:08,300 - INFO - ================================================================================
2025-08-04 18:27:08,300 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 18:27:08,371 - INFO - 找到有效目标 1: 河西集团 - http://mp.weixin.qq.com/s?__biz=MzIxOTI5OTQ2Nw==&m...
2025-08-04 18:27:08,372 - INFO - 找到有效目标 2: 南京市交通集团 - http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&m...
2025-08-04 18:27:08,372 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 18:27:08,372 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 18:27:08,372 - INFO - ============================================================
2025-08-04 18:27:08,372 - INFO - 📍 处理第 1/2 个公众号: 河西集团
2025-08-04 18:27:08,372 - INFO - ============================================================
2025-08-04 18:27:08,372 - INFO - [步骤 1/5] 为 '河西集团' 创建独立的 Cookie 抓取器...
2025-08-04 18:27:08,372 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:27:08,372 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:27:08,372 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:27:08,373 - INFO - === 开始重置网络状态 ===
2025-08-04 18:27:08,373 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:27:08,483 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:27:08,483 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:27:08,483 - INFO - 系统代理已成功关闭
2025-08-04 18:27:08,483 - INFO - ✅ 代理关闭操作
2025-08-04 18:27:08,483 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:27:10,063 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:27:10,064 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:27:10,065 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:27:10,065 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:27:10,066 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:27:10,589 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:27:10,590 - INFO - 🔄 Cookie抓取器进程已启动，PID: 41492
2025-08-04 18:27:10,590 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:27:13,591 - INFO - 等待代理服务启动...
2025-08-04 18:27:14,809 - INFO - 代理服务已启动并正常工作
2025-08-04 18:27:14,809 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 41492)
2025-08-04 18:27:14,810 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 18:27:14,810 - INFO - [步骤 2/5] 为 '河西集团' 启动 UI 自动化...
2025-08-04 18:27:14,810 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:27:14,811 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:27:14,811 - INFO - 正在查找微信主窗口...
2025-08-04 18:27:15,851 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:27:15,851 - INFO - 正在激活微信窗口...
2025-08-04 18:27:18,364 - INFO - 微信窗口已激活。
2025-08-04 18:27:18,364 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:27:25,041 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:27:25,041 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:27:28,374 - INFO - 正在查找聊天输入框...
2025-08-04 18:27:30,375 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:27:30,382 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:27:30,382 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:27:31,964 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxOTI5OTQ2Nw==&mid=2247628784&idx=1&sn=fae3bfcaaf4d24a8c22dc793cc0f0a21&chksm=97d1dc14a0a655025f55ea47c9f20cf71428c9278da4f19b4f69116ed59bbab648c58b3b4513#rd
2025-08-04 18:27:34,254 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:27:34,492 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:27:35,308 - INFO - 链接已发送。
2025-08-04 18:27:38,309 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:27:40,397 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:27:41,124 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:27:44,125 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:27:44,125 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:27:44,125 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:27:44,131 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:27:46,839 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:27:48,340 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:27:55,688 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:27:55,689 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:27:55,689 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:27:55,689 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:27:55,689 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:27:55,690 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:27:55,690 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:27:55,690 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:27:55,690 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:27:55,690 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 18:27:55,690 - INFO - [步骤 3/5] 等待 '河西集团' 的 Cookie 数据...
2025-08-04 18:27:55,690 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:27:56,691 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:27:56,692 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:27:56,693 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 18:27:56,693 - INFO - [步骤 4/5] 停止 '河西集团' 的 Cookie 抓取器...
2025-08-04 18:27:56,693 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:27:56,693 - INFO - 正在停止Cookie抓取器 (PID: 41492)...
2025-08-04 18:27:56,695 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:27:56,695 - INFO - 正在验证并清理代理设置...
2025-08-04 18:27:56,695 - INFO - === 开始重置网络状态 ===
2025-08-04 18:27:56,695 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:27:56,805 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:27:56,805 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:27:56,806 - INFO - 系统代理已成功关闭
2025-08-04 18:27:56,806 - INFO - ✅ 代理关闭操作
2025-08-04 18:27:56,806 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:27:57,762 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:27:57,763 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:27:57,763 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:27:59,931 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:27:59,932 - INFO - ✅ 网络连接验证正常
2025-08-04 18:28:02,932 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 18:28:02,933 - INFO - [步骤 5/5] 开始爬取 '河西集团' 的文章...
2025-08-04 18:28:02,934 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-04 18:28:03,039 - WARNING - ⚠️ Cookie验证失败（ret=-3），准备重新抓取Cookie...
2025-08-04 18:28:03,039 - INFO - 🔄 重新启动Cookie抓取器...
2025-08-04 18:28:03,040 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:28:03,040 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:28:03,041 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:28:03,041 - INFO - === 开始重置网络状态 ===
2025-08-04 18:28:03,041 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:28:03,138 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:28:03,139 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:28:03,139 - INFO - 系统代理已成功关闭
2025-08-04 18:28:03,139 - INFO - ✅ 代理关闭操作
2025-08-04 18:28:03,139 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:28:05,930 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:28:05,931 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:28:05,932 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:28:05,932 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:28:05,933 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:28:06,478 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:28:06,479 - INFO - 🔄 Cookie抓取器进程已启动，PID: 40372
2025-08-04 18:28:06,479 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:28:09,480 - INFO - 等待代理服务启动...
2025-08-04 18:28:14,033 - INFO - 代理服务已启动并正常工作
2025-08-04 18:28:14,034 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 40372)
2025-08-04 18:28:14,035 - INFO - 🔄 重新触发UI自动化...
2025-08-04 18:28:14,036 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:28:14,036 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:28:14,037 - INFO - 正在查找微信主窗口...
2025-08-04 18:28:14,286 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:28:14,286 - INFO - 正在激活微信窗口...
2025-08-04 18:28:16,797 - INFO - 微信窗口已激活。
2025-08-04 18:28:16,797 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:28:23,427 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:28:23,427 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:28:26,755 - INFO - 正在查找聊天输入框...
2025-08-04 18:28:28,756 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:28:28,759 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:28:28,759 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:28:30,333 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxOTI5OTQ2Nw==&mid=2247628784&idx=1&sn=fae3bfcaaf4d24a8c22dc793cc0f0a21&chksm=97d1dc14a0a655025f55ea47c9f20cf71428c9278da4f19b4f69116ed59bbab648c58b3b4513#rd
2025-08-04 18:28:32,623 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:28:32,870 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:28:33,672 - INFO - 链接已发送。
2025-08-04 18:28:36,673 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:28:38,769 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:28:39,506 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:28:42,507 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:28:42,508 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:28:42,509 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:28:42,525 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:28:45,230 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:28:46,731 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:28:54,094 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:28:54,094 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:28:54,094 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:28:54,094 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:28:54,094 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:28:54,094 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:28:54,095 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:28:54,095 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:28:54,095 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:28:54,095 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:28:55,095 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:28:55,097 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:28:55,097 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:28:55,098 - INFO - 正在停止Cookie抓取器 (PID: 40372)...
2025-08-04 18:28:55,100 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:28:55,100 - INFO - 正在验证并清理代理设置...
2025-08-04 18:28:55,101 - INFO - === 开始重置网络状态 ===
2025-08-04 18:28:55,101 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:28:55,210 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:28:55,210 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:28:55,210 - INFO - 系统代理已成功关闭
2025-08-04 18:28:55,210 - INFO - ✅ 代理关闭操作
2025-08-04 18:28:55,210 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:28:56,146 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:28:56,146 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:28:56,147 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:28:58,836 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:28:58,837 - INFO - ✅ 网络连接验证正常
2025-08-04 18:29:01,838 - INFO - ✅ 成功重新获取Cookie，继续尝试...
2025-08-04 18:29:01,838 - INFO - 🔄 第 2/2 次尝试爬取...
2025-08-04 18:29:02,112 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-04 18:30:51,409 - INFO - ✅ 公众号 '河西集团' 爬取完成！获取 10 篇文章
2025-08-04 18:30:51,409 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_河西集团_20250804_183051.xlsx
2025-08-04 18:30:51,409 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 18:31:06,410 - INFO - ============================================================
2025-08-04 18:31:06,410 - INFO - 📍 处理第 2/2 个公众号: 南京市交通集团
2025-08-04 18:31:06,410 - INFO - ============================================================
2025-08-04 18:31:06,410 - INFO - [步骤 1/5] 为 '南京市交通集团' 创建独立的 Cookie 抓取器...
2025-08-04 18:31:06,410 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:31:06,411 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:31:06,411 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:31:06,411 - INFO - === 开始重置网络状态 ===
2025-08-04 18:31:06,411 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:31:06,522 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:31:06,523 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:31:06,523 - INFO - 系统代理已成功关闭
2025-08-04 18:31:06,523 - INFO - ✅ 代理关闭操作
2025-08-04 18:31:06,523 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:31:07,551 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:31:07,551 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:31:07,552 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:31:07,552 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:31:07,553 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:31:08,086 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:31:08,087 - INFO - 🔄 Cookie抓取器进程已启动，PID: 38512
2025-08-04 18:31:08,087 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:31:11,088 - INFO - 等待代理服务启动...
2025-08-04 18:31:12,419 - INFO - 代理服务已启动并正常工作
2025-08-04 18:31:12,420 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 38512)
2025-08-04 18:31:12,420 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 18:31:12,420 - INFO - [步骤 2/5] 为 '南京市交通集团' 启动 UI 自动化...
2025-08-04 18:31:12,421 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:31:12,421 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:31:12,422 - INFO - 正在查找微信主窗口...
2025-08-04 18:31:12,674 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:31:12,674 - INFO - 正在激活微信窗口...
2025-08-04 18:31:15,186 - INFO - 微信窗口已激活。
2025-08-04 18:31:15,187 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:31:21,861 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:31:21,862 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:31:25,189 - INFO - 正在查找聊天输入框...
2025-08-04 18:31:27,190 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:31:27,200 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:31:27,201 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:31:28,776 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&mid=2247533124&idx=1&sn=bb5defd4afd5338b96e11e9328371330&chksm=eaacbf85dddb36930b5188ca9e5c6285ee8c42916252d9d14c15bf8699ade72d594e74bc5122#rd
2025-08-04 18:31:31,067 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:31:31,306 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:31:32,108 - INFO - 链接已发送。
2025-08-04 18:31:35,109 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:31:37,207 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:31:37,942 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:31:40,942 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:31:40,943 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:31:40,943 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:31:40,956 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:31:43,662 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:31:45,163 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:31:52,538 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:31:52,538 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:31:52,538 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:31:52,538 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:31:52,538 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:31:52,538 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:31:52,538 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:31:52,539 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:31:52,539 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:31:52,539 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 18:31:52,539 - INFO - [步骤 3/5] 等待 '南京市交通集团' 的 Cookie 数据...
2025-08-04 18:31:52,539 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:31:53,539 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:31:53,541 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:31:53,541 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 18:31:53,542 - INFO - [步骤 4/5] 停止 '南京市交通集团' 的 Cookie 抓取器...
2025-08-04 18:31:53,542 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:31:53,542 - INFO - 正在停止Cookie抓取器 (PID: 38512)...
2025-08-04 18:31:53,545 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:31:53,546 - INFO - 正在验证并清理代理设置...
2025-08-04 18:31:53,546 - INFO - === 开始重置网络状态 ===
2025-08-04 18:31:53,546 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:31:53,656 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:31:53,657 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:31:53,657 - INFO - 系统代理已成功关闭
2025-08-04 18:31:53,657 - INFO - ✅ 代理关闭操作
2025-08-04 18:31:53,657 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:31:57,739 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:31:57,740 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:31:57,740 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:31:58,732 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:31:58,733 - INFO - ✅ 网络连接验证正常
2025-08-04 18:32:01,733 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 18:32:01,734 - INFO - [步骤 5/5] 开始爬取 '南京市交通集团' 的文章...
2025-08-04 18:32:01,735 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-04 18:32:02,038 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-04 18:32:16,527 - INFO - ✅ 公众号 '南京市交通集团' 爬取完成！获取 1 篇文章
2025-08-04 18:32:16,527 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京市交通集团_20250804_183216.xlsx
2025-08-04 18:32:16,527 - INFO - ================================================================================
2025-08-04 18:32:16,527 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 18:32:16,527 - INFO - ================================================================================
2025-08-04 18:32:16,527 - INFO - ✅ 成功处理: 2 个公众号
2025-08-04 18:32:16,527 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 18:32:16,527 - INFO - 📄 总计文章: 11 篇
2025-08-04 18:32:16,544 - INFO - 🎉 汇总数据已保存到:
2025-08-04 18:32:16,544 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_183216.xlsx
2025-08-04 18:32:16,544 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_183216.json
2025-08-04 18:32:16,544 - INFO - ================================================================================
2025-08-04 18:32:16,544 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 18:32:16,544 - INFO - ================================================================================
2025-08-04 18:32:16,544 - INFO - ================================================================================
2025-08-04 18:32:16,545 - INFO - ✅ 全自动化爬取流程执行完毕
2025-08-04 18:32:16,545 - INFO - 详细结果请查看上方的日志输出
2025-08-04 18:32:16,545 - INFO - ================================================================================
