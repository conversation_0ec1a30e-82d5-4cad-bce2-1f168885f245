2025-08-04 18:45:34,979 - INFO - ================================================================================
2025-08-04 18:45:34,980 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-04 18:45:34,980 - INFO - ================================================================================
2025-08-04 18:45:34,980 - INFO - 版本: v3.0 - 全自动化版本
2025-08-04 18:45:34,980 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-04 18:45:34,980 - INFO - 执行时间: 2025-08-04 18:45:34
2025-08-04 18:45:34,980 - INFO - ================================================================================
2025-08-04 18:45:34,981 - INFO - 启动全新自动化爬取流程...
2025-08-04 18:45:34,981 - INFO - ================================================================================
2025-08-04 18:45:34,981 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 18:45:34,981 - INFO - ================================================================================
2025-08-04 18:45:34,981 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 18:45:35,053 - INFO - 找到有效目标 1: 南京市场监管 - http://mp.weixin.qq.com/s?__biz=MjM5Njk4NTg1OA==&m...
2025-08-04 18:45:35,053 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 18:45:35,053 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 18:45:35,053 - INFO - ============================================================
2025-08-04 18:45:35,053 - INFO - 📍 处理第 1/1 个公众号: 南京市场监管
2025-08-04 18:45:35,053 - INFO - ============================================================
2025-08-04 18:45:35,054 - INFO - [步骤 1/5] 为 '南京市场监管' 创建独立的 Cookie 抓取器...
2025-08-04 18:45:35,054 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:45:35,054 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:45:35,054 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:45:35,054 - INFO - === 开始重置网络状态 ===
2025-08-04 18:45:35,054 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:45:35,154 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:45:35,154 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:45:35,154 - INFO - 系统代理已成功关闭
2025-08-04 18:45:35,154 - INFO - ✅ 代理关闭操作
2025-08-04 18:45:35,155 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:45:36,698 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:45:36,698 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:45:36,699 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:45:36,699 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:45:36,699 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:45:37,206 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:45:37,207 - INFO - 🔄 Cookie抓取器进程已启动，PID: 35284
2025-08-04 18:45:37,207 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:45:40,208 - INFO - 等待代理服务启动...
2025-08-04 18:45:40,682 - INFO - 代理服务已启动并正常工作
2025-08-04 18:45:40,683 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 35284)
2025-08-04 18:45:40,683 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 18:45:40,683 - INFO - [步骤 2/5] 为 '南京市场监管' 启动 UI 自动化...
2025-08-04 18:45:40,684 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:45:40,684 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:45:40,684 - INFO - 正在查找微信主窗口...
2025-08-04 18:45:41,657 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:45:41,657 - INFO - 正在激活微信窗口...
2025-08-04 18:45:44,173 - INFO - 微信窗口已激活。
2025-08-04 18:45:44,174 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:45:50,810 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:45:50,810 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:45:54,142 - INFO - 正在查找聊天输入框...
2025-08-04 18:45:56,143 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:45:56,151 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:45:56,151 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:45:57,730 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5Njk4NTg1OA==&mid=2652461510&idx=1&sn=460f53668b40ef3e0dbc3c0c364ebe90&chksm=bd0d14ab8a7a9dbd1685d27b7c5953d90d5aa86cb8368da5ea6358444dce99efe0ef1f9e7aa3#rd
2025-08-04 18:46:00,019 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:46:00,240 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:46:01,042 - INFO - 链接已发送。
2025-08-04 18:46:04,043 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:46:06,131 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:46:06,859 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:46:09,859 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:46:09,860 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:46:09,861 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:46:09,871 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:46:12,577 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:46:14,079 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:46:21,518 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:46:21,519 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:46:21,519 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:46:21,519 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:46:21,519 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:46:21,519 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:46:21,520 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:46:21,520 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:46:21,520 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:46:21,520 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 18:46:21,520 - INFO - [步骤 3/5] 等待 '南京市场监管' 的 Cookie 数据...
2025-08-04 18:46:21,520 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:46:22,521 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:46:22,522 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:46:22,522 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 18:46:22,523 - INFO - [步骤 4/5] 停止 '南京市场监管' 的 Cookie 抓取器...
2025-08-04 18:46:22,523 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:46:22,524 - INFO - 正在停止Cookie抓取器 (PID: 35284)...
2025-08-04 18:46:22,526 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:46:22,526 - INFO - 正在验证并清理代理设置...
2025-08-04 18:46:22,527 - INFO - === 开始重置网络状态 ===
2025-08-04 18:46:22,527 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:46:22,635 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:46:22,635 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:46:22,635 - INFO - 系统代理已成功关闭
2025-08-04 18:46:22,635 - INFO - ✅ 代理关闭操作
2025-08-04 18:46:22,636 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:46:24,032 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:46:24,033 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:46:24,034 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:46:28,243 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:46:28,243 - INFO - ✅ 网络连接验证正常
2025-08-04 18:46:31,244 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 18:46:31,244 - INFO - [步骤 5/5] 开始爬取 '南京市场监管' 的文章...
2025-08-04 18:46:31,245 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-04 18:46:31,379 - WARNING - ⚠️ Cookie验证失败（ret=-3），准备重新抓取Cookie...
2025-08-04 18:46:31,380 - INFO - 🔄 重新启动Cookie抓取器...
2025-08-04 18:46:31,381 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:46:31,382 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:46:31,382 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:46:31,383 - INFO - === 开始重置网络状态 ===
2025-08-04 18:46:31,383 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:46:31,489 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:46:31,489 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:46:31,489 - INFO - 系统代理已成功关闭
2025-08-04 18:46:31,489 - INFO - ✅ 代理关闭操作
2025-08-04 18:46:31,489 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:46:33,827 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:46:33,828 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:46:33,829 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:46:33,829 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:46:33,830 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:46:34,330 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:46:34,331 - INFO - 🔄 Cookie抓取器进程已启动，PID: 24328
2025-08-04 18:46:34,331 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:46:37,331 - INFO - 等待代理服务启动...
2025-08-04 18:46:37,820 - INFO - 代理服务已启动并正常工作
2025-08-04 18:46:37,820 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 24328)
2025-08-04 18:46:37,820 - INFO - 🔄 重新触发UI自动化...
2025-08-04 18:46:37,821 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:46:37,821 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:46:37,821 - INFO - 正在查找微信主窗口...
2025-08-04 18:46:38,176 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:46:38,176 - INFO - 正在激活微信窗口...
2025-08-04 18:46:40,686 - INFO - 微信窗口已激活。
2025-08-04 18:46:40,686 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:46:47,429 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:46:47,430 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:46:50,758 - INFO - 正在查找聊天输入框...
2025-08-04 18:46:52,759 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:46:52,767 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:46:52,767 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:46:54,343 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5Njk4NTg1OA==&mid=2652461510&idx=1&sn=460f53668b40ef3e0dbc3c0c364ebe90&chksm=bd0d14ab8a7a9dbd1685d27b7c5953d90d5aa86cb8368da5ea6358444dce99efe0ef1f9e7aa3#rd
2025-08-04 18:46:56,633 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:46:56,874 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:46:57,690 - INFO - 链接已发送。
2025-08-04 18:47:00,691 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:47:02,788 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:47:03,524 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:47:06,525 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:47:06,526 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:47:06,527 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:47:06,538 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:47:09,243 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:47:10,744 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:47:18,102 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:47:18,103 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:47:18,103 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:47:18,103 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:47:18,103 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:47:18,103 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:47:18,103 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:47:18,103 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:47:18,104 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:47:18,104 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:47:19,105 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:47:19,106 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:47:19,107 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:47:19,108 - INFO - 正在停止Cookie抓取器 (PID: 24328)...
2025-08-04 18:47:19,111 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:47:19,111 - INFO - 正在验证并清理代理设置...
2025-08-04 18:47:19,111 - INFO - === 开始重置网络状态 ===
2025-08-04 18:47:19,112 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:47:19,232 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:47:19,233 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:47:19,233 - INFO - 系统代理已成功关闭
2025-08-04 18:47:19,233 - INFO - ✅ 代理关闭操作
2025-08-04 18:47:19,233 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:47:20,234 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:47:20,234 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:47:20,235 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:47:22,282 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:47:22,283 - INFO - ✅ 网络连接验证正常
2025-08-04 18:47:25,284 - INFO - ✅ 成功重新获取Cookie，继续尝试...
2025-08-04 18:47:25,284 - INFO - 🔄 第 2/2 次尝试爬取...
2025-08-04 18:47:25,572 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-04 18:48:38,968 - INFO - ✅ 公众号 '南京市场监管' 爬取完成！获取 6 篇文章
2025-08-04 18:48:38,968 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京市场监管_20250804_184838.xlsx
2025-08-04 18:48:38,968 - INFO - ================================================================================
2025-08-04 18:48:38,968 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 18:48:38,968 - INFO - ================================================================================
2025-08-04 18:48:38,969 - INFO - ✅ 成功处理: 1 个公众号
2025-08-04 18:48:38,969 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 18:48:38,969 - INFO - 📄 总计文章: 6 篇
2025-08-04 18:48:38,969 - INFO - ================================================================================
2025-08-04 18:48:38,969 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 18:48:38,969 - INFO - ================================================================================
2025-08-04 18:48:38,969 - INFO - ================================================================================
2025-08-04 18:48:38,969 - INFO - ✅ 全自动化爬取流程执行完毕
2025-08-04 18:48:38,969 - INFO - 详细结果请查看上方的日志输出
2025-08-04 18:48:38,970 - INFO - ================================================================================
